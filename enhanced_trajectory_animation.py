import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, FancyBboxPatch
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Sim<PERSON><PERSON>', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_trajectory(filename):
    """加载轨迹数据"""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                coords = line.split()
                if len(coords) == 2:
                    x, y = float(coords[0]), float(coords[1])
                    data.append([x, y])
    return np.array(data)

def create_enhanced_trajectory_animation():
    """创建增强版轨迹动画"""
    # 加载数据
    user_trajectory = load_trajectory('user_trajectory.txt')
    uav_trajectory = load_trajectory('uav_trajectory.txt')
    
    print(f"User trajectory points: {len(user_trajectory)}")
    print(f"UAV trajectory points: {len(uav_trajectory)}")
    
    # 确定坐标范围
    all_x = np.concatenate([user_trajectory[:, 0], uav_trajectory[:, 0]])
    all_y = np.concatenate([user_trajectory[:, 1], uav_trajectory[:, 1]])
    
    x_min, x_max = all_x.min() - 5, all_x.max() + 5
    y_min, y_max = all_y.min() - 5, all_y.max() + 5
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 主动画图
    ax1.set_xlim(x_min, x_max)
    ax1.set_ylim(y_min, y_max)
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('X Coordinate', fontsize=12)
    ax1.set_ylabel('Y Coordinate', fontsize=12)
    ax1.set_title('UAV and User Trajectory Animation', fontsize=8, fontweight='bold')
    
    # 距离变化图
    ax2.set_xlabel('Time Step', fontsize=12)
    ax2.set_ylabel('Distance', fontsize=12)
    ax2.set_title('Distance Between UAV and User', fontsize=8, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 计算所有时间步的距离
    distances = []
    for i in range(min(len(user_trajectory), len(uav_trajectory))):
        dist = np.sqrt((user_trajectory[i, 0] - uav_trajectory[i, 0])**2 + 
                      (user_trajectory[i, 1] - uav_trajectory[i, 1])**2)
        distances.append(dist)
    
    distances = np.array(distances)
    ax2.set_xlim(0, len(distances))
    ax2.set_ylim(0, max(distances) * 1.1)
    
    # 初始化轨迹线
    user_line, = ax1.plot([], [], 'b-', linewidth=2, label='User Trajectory', alpha=0.7)
    uav_line, = ax1.plot([], [], 'r-', linewidth=2, label='UAV Trajectory', alpha=0.7)
    connection_line, = ax1.plot([], [], 'g--', linewidth=1, alpha=0.5, label='Connection')
    
    # 初始化当前位置点
    user_point = Circle((0, 0), 2, color='blue', alpha=0.8, zorder=5)
    uav_point = Circle((0, 0), 2, color='red', alpha=0.8, zorder=5)
    ax1.add_patch(user_point)
    ax1.add_patch(uav_point)
    
    # 距离图线
    distance_line, = ax2.plot([], [], 'purple', linewidth=2)
    current_distance_point, = ax2.plot([], [], 'ro', markersize=8)
    
    # 添加图例
    ax1.legend(loc='upper right', fontsize=10)
    
    # 添加信息文本
    info_text = ax1.text(0.02, 0.98, '', transform=ax1.transAxes, 
                        verticalalignment='top', fontsize=10,
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 统计信息文本
    stats_text = ax2.text(0.02, 0.98, '', transform=ax2.transAxes,
                         verticalalignment='top', fontsize=10,
                         bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    def animate(frame):
        """动画函数"""
        # 计算当前帧对应的数据点数量
        max_points = min(len(user_trajectory), len(uav_trajectory))
        current_points = min(frame + 1, max_points)
        
        if current_points > 0:
            # 更新轨迹线
            user_line.set_data(user_trajectory[:current_points, 0], 
                             user_trajectory[:current_points, 1])
            uav_line.set_data(uav_trajectory[:current_points, 0], 
                            uav_trajectory[:current_points, 1])
            
            # 更新当前位置点
            current_idx = current_points - 1
            user_pos = (user_trajectory[current_idx, 0], user_trajectory[current_idx, 1])
            uav_pos = (uav_trajectory[current_idx, 0], uav_trajectory[current_idx, 1])
            
            user_point.center = user_pos
            uav_point.center = uav_pos
            
            # 更新连接线
            connection_line.set_data([user_pos[0], uav_pos[0]], [user_pos[1], uav_pos[1]])
            
            # 计算当前距离
            current_distance = distances[current_idx]
            
            # 更新距离图
            distance_line.set_data(range(current_points), distances[:current_points])
            current_distance_point.set_data([current_idx], [current_distance])
            
            # 更新信息文本
            info_text.set_text(f'Time Step: {current_points}/{max_points}\n'
                              f'User Position: ({user_pos[0]:.1f}, {user_pos[1]:.1f})\n'
                              f'UAV Position: ({uav_pos[0]:.1f}, {uav_pos[1]:.1f})\n'
                              f'Current Distance: {current_distance:.1f}')
            
            # 更新统计信息
            if current_points > 1:
                avg_distance = np.mean(distances[:current_points])
                min_distance = np.min(distances[:current_points])
                max_distance = np.max(distances[:current_points])
                
                stats_text.set_text(f'Distance Statistics:\n'
                                   f'Current: {current_distance:.1f}\n'
                                   f'Average: {avg_distance:.1f}\n'
                                   f'Min: {min_distance:.1f}\n'
                                   f'Max: {max_distance:.1f}')
        
        return (user_line, uav_line, connection_line, user_point, uav_point, 
                distance_line, current_distance_point, info_text, stats_text)
    
    # 创建动画
    max_frames = min(len(user_trajectory), len(uav_trajectory))
    anim = animation.FuncAnimation(fig, animate, frames=max_frames, 
                                 interval=200, blit=False, repeat=True)
    
    # 保存为GIF
    print("Generating enhanced GIF animation...")
    anim.save('enhanced_trajectory_animation.gif', writer='pillow', fps=5, dpi=100)
    print("Enhanced GIF animation saved as 'enhanced_trajectory_animation.gif'")
    
    # 显示动画
    plt.tight_layout()
    plt.show()
    
    return anim

def create_static_analysis():
    """创建静态分析图"""
    # 加载数据
    user_trajectory = load_trajectory('user_trajectory.txt')
    uav_trajectory = load_trajectory('uav_trajectory.txt')
    
    # 计算距离
    distances = []
    for i in range(min(len(user_trajectory), len(uav_trajectory))):
        dist = np.sqrt((user_trajectory[i, 0] - uav_trajectory[i, 0])**2 + 
                      (user_trajectory[i, 1] - uav_trajectory[i, 1])**2)
        distances.append(dist)
    
    distances = np.array(distances)
    
    # 创建静态分析图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 完整轨迹图
    ax1.plot(user_trajectory[:, 0], user_trajectory[:, 1], 'b-', linewidth=2, 
             label='User Trajectory', alpha=0.7)
    ax1.plot(uav_trajectory[:, 0], uav_trajectory[:, 1], 'r-', linewidth=2, 
             label='UAV Trajectory', alpha=0.7)
    ax1.scatter(user_trajectory[0, 0], user_trajectory[0, 1], color='blue', 
                s=100, marker='o', label='User Start', zorder=5)
    ax1.scatter(uav_trajectory[0, 0], uav_trajectory[0, 1], color='red', 
                s=100, marker='o', label='UAV Start', zorder=5)
    ax1.scatter(user_trajectory[-1, 0], user_trajectory[-1, 1], color='blue', 
                s=100, marker='s', label='User End', zorder=5)
    ax1.scatter(uav_trajectory[-1, 0], uav_trajectory[-1, 1], color='red', 
                s=100, marker='s', label='UAV End', zorder=5)
    ax1.set_xlabel('X Coordinate')
    ax1.set_ylabel('Y Coordinate')
    ax1.set_title('Complete Trajectories')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # 距离变化图
    ax2.plot(distances, 'purple', linewidth=2)
    ax2.set_xlabel('Time Step')
    ax2.set_ylabel('Distance')
    ax2.set_title('Distance Between UAV and User Over Time')
    ax2.grid(True, alpha=0.3)
    
    # 距离统计直方图
    ax3.hist(distances, bins=20, alpha=0.7, color='orange', edgecolor='black')
    ax3.set_xlabel('Distance')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Distance Distribution')
    ax3.grid(True, alpha=0.3)
    
    # 速度分析
    user_speeds = np.sqrt(np.diff(user_trajectory[:, 0])**2 + np.diff(user_trajectory[:, 1])**2)
    uav_speeds = np.sqrt(np.diff(uav_trajectory[:, 0])**2 + np.diff(uav_trajectory[:, 1])**2)
    
    ax4.plot(user_speeds, 'b-', linewidth=2, label='User Speed', alpha=0.7)
    ax4.plot(uav_speeds, 'r-', linewidth=2, label='UAV Speed', alpha=0.7)
    ax4.set_xlabel('Time Step')
    ax4.set_ylabel('Speed')
    ax4.set_title('Speed Comparison')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('trajectory_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("\n=== Trajectory Analysis ===")
    print(f"Total time steps: {len(distances)}")
    print(f"Average distance: {np.mean(distances):.2f}")
    print(f"Minimum distance: {np.min(distances):.2f}")
    print(f"Maximum distance: {np.max(distances):.2f}")
    print(f"Distance standard deviation: {np.std(distances):.2f}")
    print(f"Average user speed: {np.mean(user_speeds):.2f}")
    print(f"Average UAV speed: {np.mean(uav_speeds):.2f}")

if __name__ == "__main__":
    # 检查文件是否存在
    if not os.path.exists('user_trajectory.txt'):
        print("Error: Cannot find user_trajectory.txt file")
        exit(1)
    if not os.path.exists('uav_trajectory.txt'):
        print("Error: Cannot find uav_trajectory.txt file")
        exit(1)
    
    # 创建增强动画
    print("Creating enhanced animation...")
    animation_obj = create_enhanced_trajectory_animation()
    
    # 创建静态分析
    print("\nCreating static analysis...")
    create_static_analysis()
